# IMT Insurance Claims System - Development Kubernetes Deployment
# Single file deployment for development environment
# Usage: kubectl apply -f k8s-dev-all-in-one.yaml

---
# Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: imt-dev
  labels:
    environment: development
    app: imt-claims

---
# ConfigMap for environment variables
apiVersion: v1
kind: ConfigMap
metadata:
  name: imt-env-config
  namespace: imt-dev
data:
  DJANGO_SETTINGS_MODULE: "claims_system.settings.docker_development"
  DEBUG: "True"
  DB_ENGINE: "django.db.backends.postgresql"
  DB_NAME: "claims_db"
  DB_USER: "claims_user"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  REDIS_URL: "redis://:redis_password_2024@redis-service:6379/1"
  CELERY_BROKER_URL: "redis://:redis_password_2024@redis-service:6379/0"
  CELERY_RESULT_BACKEND: "redis://:redis_password_2024@redis-service:6379/0"
  ALLOWED_HOSTS: "*"
  CSRF_TRUSTED_ORIGINS: "https://demo.imtins.com,http://demo.imtins.com,http://*************,https://*************"
  SEED_DATABASE: "true"

---
# Secret for sensitive data
apiVersion: v1
kind: Secret
metadata:
  name: imt-secrets
  namespace: imt-dev
type: Opaque
stringData:
  SECRET_KEY: "imt_super_secret_key_for_development_2024"
  DB_PASSWORD: "claims_password_2024"
  REDIS_PASSWORD: "redis_password_2024"
  POSTGRES_PASSWORD: "claims_password_2024"

---
# ConfigMap for Nginx configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: imt-dev
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        server_tokens off;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;

        # Upstream for Django application
        upstream django_app {
            server web-service:8000;
        }

        # Development server (HTTP only)
        server {
            listen 80;
            server_name localhost demo.imtins.com www.demo.imtins.com *.localhost;

            # Security headers (basic for development)
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;

            # Static files - let Django/WhiteNoise handle them
            location /static/ {
                proxy_pass http://django_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                # Let WhiteNoise handle caching
                proxy_cache_bypass 1;
                proxy_no_cache 1;
            }

            # Media files - let Django handle them
            location /media/ {
                proxy_pass http://django_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                expires 1h;
                add_header Cache-Control "public";
            }

            # Health check
            location /health/ {
                proxy_pass http://django_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                access_log off;
            }

            # Main application
            location / {
                proxy_pass http://django_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;

                # Timeouts
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
        }
    }

---
# ConfigMap for init-db.sql
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init
  namespace: imt-dev
data:
  init-db.sql: |
    -- Create database if it doesn't exist
    SELECT 'CREATE DATABASE claims_db'
    WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'claims_db')\gexec

    -- Grant all privileges
    GRANT ALL PRIVILEGES ON DATABASE claims_db TO claims_user;

---
# PostgreSQL Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: imt-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      imagePullSecrets:
        - name: gitlab-bot
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "claims_db"
        - name: POSTGRES_USER
          value: "claims_user"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: POSTGRES_PASSWORD
        - name: POSTGRES_INITDB_ARGS
          value: "--encoding=UTF-8"
        volumeMounts:
        - name: postgres-init
          mountPath: /docker-entrypoint-initdb.d
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - claims_user
            - -d
            - claims_db
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - claims_user
            - -d
            - claims_db
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-init
        configMap:
          name: postgres-init

---
# PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: imt-dev
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
# Redis Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: imt-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      imagePullSecrets:
        - name: gitlab-bot
      containers:
      - name: redis
        image: redis:7-alpine
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --requirepass
        - "redis_password_2024"
        ports:
        - containerPort: 6379
        livenessProbe:
          exec:
            command:
            - redis-cli
            - --raw
            - incr
            - ping
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - --raw
            - incr
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: imt-dev
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP

---
# Django Web Application Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web
  namespace: imt-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: web
  template:
    metadata:
      labels:
        app: web
    spec:
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
      # Wait for PostgreSQL
      - name: wait-for-postgres
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z postgres-service 5432; do echo waiting for postgres; sleep 2; done;']
      # Run migrations
      - name: migrate
        image: registry.imtins.com:5005/carter.minear/demo-claims:main
        imagePullPolicy: Always
        command: ["python", "manage.py", "migrate"]
        envFrom:
        - configMapRef:
            name: imt-env-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: DB_PASSWORD
      # Collect static files
      - name: collectstatic
        image: registry.imtins.com:5005/carter.minear/demo-claims:main
        imagePullPolicy: Always
        command: ["sh", "-c", "mkdir -p /app/staticfiles /app/media /app/logs && python manage.py collectstatic --noinput"]
        envFrom:
        - configMapRef:
            name: imt-env-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: DB_PASSWORD
      # Seed database
      - name: seed-database
        image: registry.imtins.com:5005/carter.minear/demo-claims:main
        imagePullPolicy: Always
        command: ["sh", "-c", "mkdir -p /app/staticfiles /app/media /app/logs && python manage.py create_demo_superuser || true && python manage.py seed_database || true && python manage.py create_sample_users || true"]
        envFrom:
        - configMapRef:
            name: imt-env-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: DB_PASSWORD
      containers:
      - name: web
        image: registry.imtins.com:5005/carter.minear/demo-claims:main
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
        command: ["sh", "-c", "mkdir -p /app/staticfiles /app/media /app/logs && /entrypoint.sh gunicorn claims_system.wsgi:application --bind 0.0.0.0:8000 --workers 3"]
        envFrom:
        - configMapRef:
            name: imt-env-config
        env:
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: SECRET_KEY
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: imt-secrets
              key: DB_PASSWORD
        livenessProbe:
          httpGet:
            path: /health/
            port: 8000
          initialDelaySeconds: 40
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health/
            port: 8000
          initialDelaySeconds: 20
          periodSeconds: 10

---
# Django Service
apiVersion: v1
kind: Service
metadata:
  name: web-service
  namespace: imt-dev
spec:
  selector:
    app: web
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP

---
# Nginx Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx
  namespace: imt-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      imagePullSecrets:
        - name: gitlab-bot
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        livenessProbe:
          httpGet:
            path: /health/
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health/
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config

---
# Nginx Service (ClusterIP for Ingress)
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
  namespace: imt-dev
spec:
  selector:
    app: nginx
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP

---
# Ingress for external access via existing NGINX LoadBalancer
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: imt-claims-ingress
  namespace: imt-dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
spec:
  tls:
  - hosts:
    - demo.imtins.com
    secretName: imtins
  rules:
  - host: demo.imtins.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx-service
            port:
              number: 80
  - http:  # Default rule for IP access
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx-service
            port:
              number: 80